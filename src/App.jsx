import { useState } from 'react'
import { motion } from 'framer-motion'
import { Settings, Moon, Sun } from 'lucide-react'
import { Button } from './components/ui/button'
import useStore from './store/useStore'
import './App.css'

import * as PIXI from 'pixi.js'
import { Live2DModel } from 'pixi-live2d-display'

function App() {
  const [count, setCount] = useState(0)
  const { isDarkMode, toggleDarkMode, isSettingsOpen, setSettingsOpen } = useStore()

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'
    }`}>
      <div className="container mx-auto px-4 py-8">
        <canvas></canvas>
      </div>
    </div>
  )
}

export default App
