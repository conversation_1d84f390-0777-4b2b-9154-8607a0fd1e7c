import { useState, useRef, useEffect } from 'react'
import './App.css'

import * as PIXI from 'pixi.js'
import { Live2DModel } from 'pixi-live2d-display'

// 设置全局 PIXI 对象，Live2D 需要
window.PIXI = PIXI

function App() {
  const canvasRef = useRef(null)
  const [app, setApp] = useState(null)
  const [model, setModel] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    let pixiApp = null
    let live2dModel = null
    const initLive2D = async () => {
      try {
        setLoading(true)
        setError(null)

        // 创建 PIXI 应用
        pixiApp = new PIXI.Application({
          view: canvasRef.current,
          width: 1024,
          height: 1024,
          backgroundColor: 0xffffff,
          antialias: true,
          resolution: window.devicePixelRatio || 1,
          autoDensity: true,
        })

        // 设置应用到 state
        setApp(pixiApp)

        // 加载 Live2D 模型
        live2dModel = await Live2DModel.from('/live2d/Idol/ldol.model3.json')

        // 添加模型到舞台
        pixiApp.stage.addChild(live2dModel)

        // 设置模型位置和缩放
        live2dModel.position.set(pixiApp.screen.width / 2, pixiApp.screen.height / 2)
        live2dModel.scale.set(0.5)

        // 添加更新循环
        const updateTicker = () => {
          if (live2dModel) {
            live2dModel.update()
          }
        }
        pixiApp.ticker.add(updateTicker)

        // 设置模型到 state
        setModel(live2dModel)
        setLoading(false)

      } catch (err) {
        console.error('Live2D 初始化失败:', err)
        setError(err.message || '加载模型失败')
        setLoading(false)
      }
    }

    // 确保 canvas 元素存在后再初始化
    if (canvasRef.current) {
      initLive2D()
    }

    // 清理函数
    return () => {
      if (live2dModel) {
        live2dModel.destroy()
      }
      if (pixiApp) {
        pixiApp.destroy(true, true)
      }
    }
  }, [])

  // 渲染加载状态
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载 Live2D 模型...</p>
        </div>
      </div>
    )
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen transition-colors duration-300 bg-white text-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center">
          <h1 className="text-3xl font-bold mb-8 text-center">Live2D 展示</h1>
          <div className="relative">
            <canvas
              ref={canvasRef}
              className="border border-gray-200 rounded-lg shadow-lg"
            />
            {model && (
              <div className="mt-4 text-center text-sm text-gray-500">
                模型已加载完成
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
