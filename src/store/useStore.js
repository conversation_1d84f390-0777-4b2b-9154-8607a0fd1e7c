import { create } from 'zustand'

const useStore = create((set) => ({
  // Live2D 相关状态
  live2dModel: null,
  isModelLoaded: false,
  modelScale: 1,
  modelPosition: { x: 0, y: 0 },
  
  // UI 状态
  isSettingsOpen: false,
  isDarkMode: false,
  
  // Actions
  setLive2dModel: (model) => set({ live2dModel: model }),
  setModelLoaded: (loaded) => set({ isModelLoaded: loaded }),
  setModelScale: (scale) => set({ modelScale: scale }),
  setModelPosition: (position) => set({ modelPosition: position }),
  setSettingsOpen: (open) => set({ isSettingsOpen: open }),
  toggleDarkMode: () => set((state) => ({ isDarkMode: !state.isDarkMode })),
}))

export default useStore
