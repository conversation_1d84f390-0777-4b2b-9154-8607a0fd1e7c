# Live2D Electron 项目配置说明

## 已安装的依赖包

### UI 组件和动画库
- `lucide-react` (0.534.0) - 现代化图标库
- `framer-motion` (12.23.11) - 强大的动画库
- `vaul` (1.1.2) - 抽屉组件库

### 样式相关
- `tailwindcss` (4.1.11) - 实用优先的 CSS 框架
- `autoprefixer` (10.4.21) - CSS 自动前缀工具
- `postcss` (8.5.6) - CSS 后处理器
- `tailwindcss-animate` (1.0.7) - Tailwind 动画扩展
- `tailwind-merge` (3.3.1) - Tailwind 类名合并工具
- `clsx` (2.1.1) - 条件类名工具
- `class-variance-authority` (0.7.1) - 组件变体管理

### 状态管理和工具
- `zustand` (5.0.6) - 轻量级状态管理库
- `react-intersection-observer` (9.16.0) - 交叉观察器 Hook

### Radix UI 组件
- `@radix-ui/react-dialog` (1.1.14) - 无障碍对话框组件
- `@radix-ui/react-slot` (1.2.3) - 插槽组件
- `@radix-ui/react-separator` (1.1.7) - 分隔符组件

## 配置文件

### 1. Tailwind CSS 配置 (`tailwind.config.js`)
- 配置了内容路径扫描
- 添加了自定义动画和关键帧
- 集成了 `tailwindcss-animate` 插件

### 2. PostCSS 配置 (`postcss.config.js`)
- 集成了 Tailwind CSS 和 Autoprefixer

### 3. 样式文件 (`src/index.css`)
- 添加了 Tailwind 指令
- 保留了原有的自定义样式

## 项目结构

```
src/
├── components/
│   └── ui/
│       └── button.jsx          # 可复用的按钮组件
├── lib/
│   └── utils.js               # 工具函数 (cn 函数)
├── store/
│   └── useStore.js            # Zustand 状态管理
├── App.jsx                    # 主应用组件 (已更新)
├── index.css                  # 全局样式
└── main.jsx                   # 应用入口
```

## 使用示例

### 1. 使用 Button 组件
```jsx
import { Button } from './components/ui/button'

<Button variant="outline" size="lg">
  点击我
</Button>
```

### 2. 使用 Zustand 状态管理
```jsx
import useStore from './store/useStore'

function MyComponent() {
  const { isDarkMode, toggleDarkMode } = useStore()
  
  return (
    <button onClick={toggleDarkMode}>
      {isDarkMode ? '切换到亮色' : '切换到暗色'}
    </button>
  )
}
```

### 3. 使用 Framer Motion 动画
```jsx
import { motion } from 'framer-motion'

<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.5 }}
>
  内容
</motion.div>
```

### 4. 使用 Lucide React 图标
```jsx
import { Settings, Moon, Sun } from 'lucide-react'

<Settings className="h-4 w-4" />
```

## 开发命令

- `pnpm dev` - 启动开发服务器
- `pnpm build` - 构建生产版本
- `pnpm preview` - 预览构建结果

## 注意事项

1. 已将 Vite 版本降级到 5.4.19 以解决兼容性问题
2. 所有组件都支持暗色模式
3. 使用 `cn()` 函数来合并 Tailwind 类名
4. 状态管理使用 Zustand，比 Redux 更轻量
5. 所有 UI 组件都基于 Radix UI，确保无障碍访问

## 下一步

1. 集成 Live2D 模型加载功能
2. 添加更多 UI 组件 (Dialog, Drawer, etc.)
3. 完善状态管理结构
4. 添加 Electron 集成
